import { useCallback } from 'react'
import PrefundAdjustment from './PrefundAdjustment'
import usePrefundTransaction from '@/hooks/Account/usePrefundTransaction'
import ModalProvider from '@/contexts/ModalProvider'
import useAuth from '@/hooks/useAuth'
import { GL_ADMINISTRATION } from '@/constants/cognitoGroupsTypes'
import schema from './schema'

const PrefundAdjustmentContainer = (props) => {
    const { isAllowed } = useAuth()
    const mutation = usePrefundTransaction()
    const isAdmin = isAllowed(GL_ADMINISTRATION)

    const handleSubmit = (values) => {
        console.log(values)
        if (isAdmin) {
            mutation.mutate({
                ...values,
                profileId: props?.data?.profileId,
                accountId: props?.data?.accountId
            })
        }
    }

    return (
        <ModalProvider
            edit={props.edit}
            schema={schema}
            handleClose={props.handleClose}
            show={props.show}
            handleSubmit={handleSubmit}
        >
            <PrefundAdjustment {...props} />
        </ModalProvider>
    )
}

export default PrefundAdjustmentContainer
