// constants/columns/transactionSearch.js
import generateDate from "@/utils/generateDate";
import amountFormat from "@/utils/amountFormat";
import IconButton from "@mui/material/IconButton";
import EditIcon from "@mui/icons-material/Edit";

// Base transaction columns
const columns = [
  {
    headerName: "Transaction ID",
    field: "transaction_ref_id",
    minWidth: 150,
    flex: 1,
  },
  {
    headerName: "Instruction ID",
    field: "instruction_ref_id",
    minWidth: 150,
    flex: 1,
  },
  {
    headerName: "Acceptance Date",
    field: "acceptance_date_time",
    flex: 1,
    valueGetter: (value) => generateDate(value),
  },
  {
    headerName: "Amount",
    field: "amount",
    flex: 1,
    valueGetter: (value) => `${amountFormat(value)}`,
  },
];

/**
 * Generates action columns for the transaction table, including an Edit MetaData button.
 * @param {function} openMetaDataPopup - Callback to open the MetaData popup with instruction, transaction, and account IDs.
 * @param {boolean} isAdmin - Indicates if the user has GL_ADMINISTRATION role.
 * @returns {Array} Array of action column definitions.
 */
export const actionColumns = (openMetaDataPopup, isAdmin) => [
  {
    headerName: "Additional Details",
    field: "additional_details",
    flex: 1,
    renderCell: ({ row }) => {
      // Only render the button for GL_ADMINISTRATION users
      if (!isAdmin) return null;

      const { instruction_ref_id, transaction_ref_id, account_ref_id } = row;

      return (
        <IconButton
          onClick={(e) => {
            e.stopPropagation();
            openMetaDataPopup(
              instruction_ref_id,
              transaction_ref_id,
              account_ref_id
            );
          }}
          aria-label="Edit transaction metadata"
          size="small"
        >
          <EditIcon />
        </IconButton>
      );
    },
  },
];

export default columns;
